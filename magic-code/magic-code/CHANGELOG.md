# Releases note

Welcome to use AliOS Studio Magic Code.
AliOS Studio Magic Code(As Magic Code) is the new official IDE for AliOS development.
It implemented most major functions working in AliOS Studio, and support the new favorite development environment vscode.

## Release notes 2.6.4

Features integrated:

* Implement AliOS Studio remote automatic updates.
* Implement SDK remote automatic updates.
* Support setting the temporary directory location on the device before app running.

User experience improvements:

* Improve user information interface, Support to config the "Access Channel" and "SDK Release Phase" on user information interface.
* Support for switching between Chinese and English interfaces.
* When run a cloud app, identify compatible ETSC version automatically and prompt users to download.
* Classify SDK tools.

Bugfix:

* Update signature tool to support all version of current java.

## Release notes 2.6.3
Features integrated:

- Release new version of emulator management ---- Virtual Device Manager,the creation process steps are integrated and standardized，add the CPU core configuration function of the emulator,
support custom local import of image，the download of image SDK supports functions such as pause and resume;


Bugfix:

- fix UIAutomatorViewer open fail in windows;

## Release notes 2.6.2

Features integrated:

- Integrated Flutter Project, Support the creation, compilation, running, hot loading and other functions.
- Integrated Unity Project, Support project development and running.

User experience improvements:

- If the operating system is windows select the disk with the most space left as the sdk root path.
- Auto install the dependencies of project when create project.

Bugfix:

- Clean the user token when log out.
- Fix packing app error when symbol links are included in the app.

## Release notes 2.6.1

Features integrated:

- remove cloud emulator form tool list.
- add EP33L and AS32 products for creating project.

User experience improvements:

- Remind the user to log in to ide or update login token when sdk can not get artifact list from server.
- Modify the default values of ramSize, sdSize and storageSize when the simulator is created

Bugfix:

- Fixed the bug can not build cpp template file by yidlc tools on mac.
- Restore the default channel when user log out. 

## Release notes 2.6.0

Features integrated:

- Add magic toolkit pane and add spider.
- Support cockpit development.
- Emulator supports multiscreen layout.

User experience improvements:

- Fixed some layout problems in home page.
- Support custom NDK compilation parameters.


## Release notes 2.2.0

Features integrated:

- Add project attributes and business SDK access, update CAF/HDT/HDT-UI every day, and support lite2.0 project application development
- ETSC compilation parameters can be configured and combined to add a independent ETSC compilation command
- Precheck online code reviews and code specifications
- Add intelligent code completion function (need to open manually)
- Add tool to find possible code segments and historical bugs automatically
- Add View Debugger
- Add hiding non-important components and the option of mandatory update in SDK
- Add Linux 64-bit docker version of adb(adb_docker)
- Added sendlink option and pre/post scripts option before application runs

User experience improvements:

- Adjust the project template and sample code displayed in webview to avoid occupying the capability menu space
- Adjust log output such as run /debug && increase phased log output

Bugfix:
- Fix the prompt during Manifest checking and completion
- The log information at project run/compile time is adjusted from the notification to Output panel
- Fixed the lack of system-images directory when the simulator started
- fixed some UI issues
- Fixed SDK set jump could not locate accurately
- Solve issue that adb dependent tools such as UIAutomator cannot be uninstalled by sdkmanager

## Release notes 2.1.0-RC

Features integrated:

- Add UI Automator

User experience improvements:

- AliOS Studio and sdk seperate into two categories
- Merge 7 vsixs together to 1

## Release notes 2.0.2-RC


Features integrated:

- Remote ndk compiling
- Support release server token


User experience improvements:

- Can import artifact into sdk manager
- Support pause/continue/break during downloading
- Now system images are independent from emulator
- CAF d.ts/project and file templates ship with sdk
- File template can be triggered in the context menu


Bug fixed:

- Improve ndk compiling prompt

## Release notes 2.0.0-RC.1



The first version integrated with sdk and used sdk manager for updating components.

We also provide TS project and debug support in this version.

We encourage everyone to upgrade to this version.



Features integrated:

- sdk manager

- debugger for typescript application

- debugger now support F5 breakpoints

- Add signature creation support



User experience improvements:

- When only one device is connected, the device is selected by default



Bug fixed:

- Fixed metrics uploading issue

- Fixed update window refreshing issue

- Can create a signature with the same name



## Release notes 2.0.0-beta.1



Initial release including functions as listed:



- Provide index page for AliOS functions

- Create AliOS project from templates

 - support online templates

- Run AliOS project

 - support multiple projects

 - support multiple devices

 - support multiple pages

 - support three types of signature methods

- Debug AliOS project both on device and Emulator

- History for Run and Debug

- AliOS Logcat

 - support multiple log buffer switch

 - support filter by tag and id

 - support to save log buffer onto file

- Import AliOS project

- Import AliOS sample project

- Export AliOS project to device

- Install AliOS application

- CAF API code completion

- component management and update

 - support download pause and resume

 - support import for package

- Manage AliOS Emulator and virtual devices

